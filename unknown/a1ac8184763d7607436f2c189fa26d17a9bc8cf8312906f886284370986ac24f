# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name snownavi.ski www.snownavi.ski;
    return 301 https://$host$request_uri;
}

# HTTPS server block
server {
    listen 443 ssl;
    server_name snownavi.ski www.snownavi.ski;

    # File upload settings
    client_max_body_size 16M;        # Allow uploads up to 16MB
    client_body_buffer_size 128k;    # Increase buffer size for file uploads
    client_body_timeout 60s;         # Increase timeout for larger uploads
    client_header_timeout 60s;       # Increase header timeout
    keepalive_timeout 60s;           # Increase keepalive timeout
    send_timeout 60s;                # Increase send timeout

    ssl_session_timeout 5m;

    ssl_certificate /etc/nginx/snownavi.ski_bundle.pem; # Path to your SSL certificate
    ssl_certificate_key /etc/nginx/snownavi.ski.key; # Path to your SSL private key

    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;

    ssl_protocols TLSv1.2 TLSv1.3; # Recommended protocols
    ssl_prefer_server_ciphers on;

    # Admin and API endpoints handled by Flask server
    location ~ ^/(course_admin\.html|login\.html|auth_callback\.html|api/|data/|uploads/) {
        proxy_pass http://localhost:8899;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_request_buffering off;       # Disable request buffering for large file uploads
        proxy_buffering off;               # Disable response buffering
    }

    # Default location block for handling general requests
    # 📄 Serve the web frontend (index.html, course.html, 404.html, assets etc)
    location / {
        root /home/<USER>/git/snownavi_website;
        index index.html;
        try_files $uri $uri/ /index.html;
        error_page 404 /404.html;
    }

    location /course.html {
        root /home/<USER>/git/snownavi_website;
        try_files $uri =404;
    }

    location /404.html {
        root /home/<USER>/git/snownavi_website;
    }

    location /classic/ {
        proxy_pass http://localhost:9966;  # Assuming this is your main service
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /route/ {
        proxy_pass http://localhost:5000/route/v1/;  # Assuming this is your main service
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /route/morzine/ {
        proxy_pass http://localhost:5001/route/v1/;  # Assuming this is your main service
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /route/palarinsal/ {
        proxy_pass http://localhost:5002/route/v1/;  # Assuming this is your main service
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /preview {
        alias /home/<USER>/git/snownavi_flutter;  # Path to your Flutter build output
        try_files $uri $uri/ /index.html =404;
    }

}

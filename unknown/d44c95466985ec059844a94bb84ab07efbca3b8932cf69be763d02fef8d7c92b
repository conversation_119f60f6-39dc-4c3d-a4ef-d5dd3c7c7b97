[Unit]
Description=SnowNavi Flask Server
After=network.target
Wants=network-online.target

[Service]
User=lighthouse
Group=lighthouse
WorkingDirectory=/home/<USER>/git/snownavi_website
Environment="PATH=/home/<USER>/git/snownavi_website/venv/bin"
Environment="PYTHONUNBUFFERED=1"
# Load environment variables from .env file
EnvironmentFile=/home/<USER>/git/snownavi_website/backend/.env
ExecStart=/home/<USER>/git/snownavi_website/venv/bin/python /home/<USER>/git/snownavi_website/backend/server.py
Restart=always
RestartSec=5
# Use journal instead of syslog for modern systemd versions
StandardOutput=journal
StandardError=journal
SyslogIdentifier=snownavi-flask
# Add security settings
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
